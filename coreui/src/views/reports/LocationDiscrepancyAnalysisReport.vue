<template>
  <c-col col="12" lg="12">
    <filter-data @getLocationDiscrepancy="filter"/>

    <!-- Loading State -->
    <c-card v-if="isLoading">
      <c-card-body class="text-center">
        <c-spinner size="lg" class="mb-3"/>
        <h5>Analyzing location discrepancies...</h5>
        <p class="text-muted">This may take a few moments for large datasets.</p>
      </c-card-body>
    </c-card>

    <!-- Summary Statistics Section -->
    <c-card v-if="summaryStats && !isLoading">
      <c-card-header>
        <h4 class="text-center">
          Location Discrepancy Analysis Summary
          <br/>
          <small class="text-muted">
            From: {{ filterData.from_date }} to: {{ filterData.to_date }}
            | Distance Threshold: {{ filterData.distance_threshold }}m
          </small>
        </h4>
      </c-card-header>
      <c-card-body>
        <div class="row">
          <div class="col-lg-3 col-md-6 col-sm-12">
            <div class="card bg-primary text-white">
              <div class="card-body text-center">
                <h3>{{ summaryStats.total_accounts_analyzed }}</h3>
                <p class="mb-0">Total Accounts Analyzed</p>
              </div>
            </div>
          </div>
          <div class="col-lg-3 col-md-6 col-sm-12">
            <div class="card bg-warning text-white">
              <div class="card-body text-center">
                <h3>{{ summaryStats.accounts_with_discrepancies }}</h3>
                <p class="mb-0">Accounts with Discrepancies</p>
              </div>
            </div>
          </div>
          <div class="col-lg-3 col-md-6 col-sm-12">
            <div class="card bg-info text-white">
              <div class="card-body text-center">
                <h3>{{ summaryStats.total_visits_analyzed }}</h3>
                <p class="mb-0">Total Visits Analyzed</p>
              </div>
            </div>
          </div>
          <div class="col-lg-3 col-md-6 col-sm-12">
            <div class="card bg-success text-white">
              <div class="card-body text-center">
                <h3>{{ summaryStats.average_distance_variance }}m</h3>
                <p class="mb-0">Avg Distance Variance</p>
              </div>
            </div>
          </div>
        </div>
        <div class="row mt-3">
          <div class="col-lg-4 col-md-6 col-sm-12">
            <div class="card bg-danger text-white">
              <div class="card-body text-center">
                <h3>{{ summaryStats.maximum_distance_variance }}m</h3>
                <p class="mb-0">Max Distance Variance</p>
              </div>
            </div>
          </div>
          <div class="col-lg-4 col-md-6 col-sm-12">
            <div class="card bg-secondary text-white">
              <div class="card-body text-center">
                <h3>{{ summaryStats.accounts_without_registered_location }}</h3>
                <p class="mb-0">No Registered Location</p>
              </div>
            </div>
          </div>
          <div class="col-lg-4 col-md-6 col-sm-12">
            <div class="card bg-dark text-white">
              <div class="card-body text-center">
                <h3>{{ summaryStats.accounts_without_visits }}</h3>
                <p class="mb-0">No Visits in Period</p>
              </div>
            </div>
          </div>
        </div>
      </c-card-body>
    </c-card>

    <!-- Data Table Section -->
    <c-card v-if="reportData.length > 0 && !isLoading">
      <c-card-header>
        <h4>Account-Level Analysis Results</h4>
      </c-card-header>
      <c-card-body>
        <!-- Vue2DataTable for Performance -->
        <Vue2DataTable
          :columns="tableColumns"
          :data-source="reportData"
          :virtual-scroll-enabled="reportData.length > 1000"
          :column-virtualization-enabled="tableColumns.length > 10"
          :show-search="true"
          :show-pagination="reportData.length <= 1000"
          :show-total-bar="true"
          :row-height="60"
          :column-width="200"
          :virtual-scroll-threshold="1000"
          search-placeholder="Search accounts..."
          @row-click="handleRowClick"
          @selection-change="handleSelectionChange"
        >
          <!-- Custom slot for has_discrepancies column -->
          <template #has_discrepancies="{ value }">
            <span :class="value ? 'badge badge-danger' : 'badge badge-success'">
              {{ value ? 'Yes' : 'No' }}
            </span>
          </template>

          <!-- Custom slot for max_distance column -->
          <template #max_distance_from_registered_location="{ value }">
            <span :class="getDistanceClass(value)">
              {{ value ? value + 'm' : 'N/A' }}
            </span>
          </template>

          <!-- Custom slot for registered_location column -->
          <template #registered_location="{ value }">
            <span v-if="value && value.latitude && value.longitude" class="text-success">
              {{ value.latitude.toFixed(6) }}, {{ value.longitude.toFixed(6) }}
            </span>
            <span v-else class="text-muted">Not Set</span>
          </template>

          <!-- Custom slot for actions -->
          <template #actions="{ item }">
            <c-button
              v-if="item.visits_count > 0"
              color="info"
              size="sm"
              @click="toggleVisitDetails(item)"
              class="mr-1"
            >
              <c-icon name="cil-list"/>
              {{ expandedRows.includes(item.account_id) ? 'Hide' : 'Show' }} Visits
            </c-button>
            <c-button
              v-if="item.registered_location.has_location"
              color="primary"
              size="sm"
              @click="showOnMap(item)"
            >
              <c-icon name="cil-location-pin"/>
              Map
            </c-button>
          </template>
        </Vue2DataTable>
      </c-card-body>
      <c-card-footer>
        <download
          @getPrint="print"
          @getxlsx="download"
          @getpdf="createPDF"
          @getcsv="downloadCsv"
          :fields="exportFields"
          :data="exportData"
          :name="reportName"
        />
      </c-card-footer>
    </c-card>

    <!-- Expandable Visit Details -->
    <c-card v-for="accountId in expandedRows" :key="'visits-' + accountId" class="mt-3">
      <c-card-header>
        <h5>Visit Details for Account: {{ getAccountName(accountId) }}</h5>
      </c-card-header>
      <c-card-body>
        <Vue2DataTable
          :columns="visitColumns"
          :data-source="getVisitDetails(accountId)"
          :show-search="true"
          :show-pagination="true"
          :row-height="50"
          search-placeholder="Search visits..."
        >
          <!-- Custom slot for distance_from_registered_location -->
          <template #distance_from_registered_location="{ value }">
            <span :class="getDistanceClass(value)">
              {{ value ? value + 'm' : 'N/A' }}
            </span>
          </template>

          <!-- Custom slot for exceeds_threshold -->
          <template #exceeds_threshold="{ value }">
            <span :class="value ? 'badge badge-danger' : 'badge badge-success'">
              {{ value ? 'Yes' : 'No' }}
            </span>
          </template>

          <!-- Custom slot for visit_location -->
          <template #visit_location="{item}">
            <span v-if="item && item.latitude && item.longitude" class="text-info">
              {{ item }}
            </span>
            <span  v-else class="text-muted">Not Available</span>
          </template>
        </Vue2DataTable>
      </c-card-body>
    </c-card>

    <!-- No Data Message -->
    <c-card v-if="!isLoading && reportData.length === 0 && hasSearched">
      <c-card-body class="text-center">
        <c-icon name="cil-info" size="3xl" class="text-muted mb-3"/>
        <h4 class="text-muted">No Data Found</h4>
        <p class="text-muted">
          No location discrepancy data found for the selected criteria.
          Try adjusting your filters or date range.
        </p>
      </c-card-body>
    </c-card>
  </c-col>
</template>

<script>
import filterData from "../../components/reports/LocationDiscrepancyAnalysis/filterData.vue";
import download from "../../components/download-reports/download.vue";
import Vue2DataTable from "../../components/common/Vue2DataTable/components/core/Vue2DataTable.vue";
import {Amiri} from "../../assets/fonts/Amiri-Regular-normal";
import jsPDF from "jspdf";
import "jspdf-autotable";
import mixins from "../../mixins";
import LargeDatasetHandler from "../../mixins/LargeDatasetHandler.js";

export default {
  mixins: [mixins, LargeDatasetHandler],
  components: {
    filterData,
    download,
    Vue2DataTable,
  },
  data() {
    return {
      isLoading: false,
      hasSearched: false,
      reportData: [],
      summaryStats: null,
      filterData: {},
      expandedRows: [],
      reportName: "Location Discrepancy Analysis Report",

      // Table columns for main data
      tableColumns: [
        {key: 'account_id', label: 'Account ID', sortable: true},
        {key: 'account_name', label: 'Account Name', sortable: true,},
        {key: 'account_code', label: 'Account Code', sortable: true,},
        {key: 'account_address', label: 'Address', sortable: true,},
        {key: 'registered_location', label: 'Registered Location', sortable: false,},
        {key: 'visits_count', label: 'Visits Count', sortable: true,},
        {key: 'has_discrepancies', label: 'Has Discrepancies', sortable: true,},
        {key: 'max_distance_from_registered_location', label: 'Max Distance', sortable: true,},
        {key: 'distance_threshold_used', label: 'Threshold Used', sortable: true,},
        {key: 'actions', label: 'Actions', sortable: false,},
      ],

      // Table columns for visit details
      visitColumns: [
        {key: 'visit_id', label: 'Visit ID', sortable: true},
        {key: 'visit_date', label: 'Visit Date', sortable: true},
        {
          key: 'visit_location',
          label: 'Visit Location',
          sortable: false,
          virtual: true,
          formatter: (item) => `${item.latitude},${item.longitude}`,
        },
        {key: 'user_name', label: 'User Name', sortable: true},
        {key: 'visit_frequency', label: 'Frequency', sortable: true},
        {key: 'distance_from_registered_location', label: 'Distance', sortable: true},
        {key: 'exceeds_threshold', label: 'Exceeds Threshold', sortable: true},
      ],
    };
  },
  computed: {
    exportFields() {
      return this.tableColumns
        .filter(col => col.key !== 'actions')
        .map(col => col.key);
    },

    exportData() {
      return this.reportData.map(item => {
        const exportItem = {...item};
        // Format registered_location for export
        if (exportItem.registered_location && exportItem.registered_location.latitude) {
          exportItem.registered_location = `${exportItem.registered_location.latitude}, ${exportItem.registered_location.longitude}`;
        } else {
          exportItem.registered_location = 'Not Set';
        }
        // Remove actions column
        delete exportItem.actions;
        return exportItem;
      });
    },
  },
  emits: ["downloaded"],
  methods: {
    async filter({filters}) {
      this.filterData = filters;
      this.hasSearched = true;
      this.expandedRows = [];

      // Initialize large dataset processing
      if (!this.initializeLargeDatasetProcessing('Analyzing Location Discrepancies')) {
        return;
      }

      this.isLoading = true;

      try {
        // Update progress
        this.updateLargeDatasetProgress(10, 'Sending request to server...');

        const {res: response, error} = await this.tryCatch(
          axios.post("/api/account-location-comparison", filters)
        );

        if (error) {
          this.handleLargeDatasetError(error, 'API request');
          return;
        }

        this.updateLargeDatasetProgress(50, 'Processing server response...');

        if (response.data.data.success) {
          this.summaryStats = response.data.data.summary;

          // Check if dataset is large and warn user
          const accountCount = response.data.data.accounts.length;
          if (accountCount > 10000) {
            if (!this.shouldProceedWithLargeDataset(accountCount, 'location analysis')) {
              this.finalizeLargeDatasetProcessing();
              this.isLoading = false;
              return;
            }
          }

          this.updateLargeDatasetProgress(80, 'Loading data into table...');

          // Process data in chunks for large datasets
          if (accountCount > 5000) {
            await this.processLargeDataset(response.data.data.accounts);
          } else {
            this.reportData = response.data.data.accounts;
          }

          this.updateLargeDatasetProgress(100, 'Analysis complete');

          // Show success message with statistics
          this.flash(
            `Analysis complete: ${accountCount} accounts analyzed, ${this.summaryStats.accounts_with_discrepancies} with discrepancies`,
            'success'
          );

        } else {
          this.handleLargeDatasetError(
            new Error("Failed to generate report"),
            'server response'
          );
        }
      } catch (error) {
        this.handleLargeDatasetError(error, 'location analysis');
      } finally {
        this.finalizeLargeDatasetProcessing();
        this.isLoading = false;
      }
    },

    async processLargeDataset(accounts) {
      try {
        // Process accounts in chunks to prevent UI blocking
        const chunkSize = 1000;
        this.reportData = [];

        for (let i = 0; i < accounts.length; i += chunkSize) {
          const chunk = accounts.slice(i, i + chunkSize);
          this.reportData.push(...chunk);

          // Update progress
          const progress = 80 + (20 * (i + chunkSize) / accounts.length);
          this.updateLargeDatasetProgress(progress, `Loading ${i + chunkSize}/${accounts.length} accounts...`);

          // Allow UI to update
          await this.$nextTick();

          // Small delay to prevent browser freezing
          if (i > 0 && i % 5000 === 0) {
            await new Promise(resolve => setTimeout(resolve, 100));
          }
        }
      } catch (error) {
        throw new Error(`Failed to process large dataset: ${error.message}`);
      }
    },

    handleRowClick(item) {
      // Optional: Handle row click events
      console.log('Row clicked:', item);
    },

    handleSelectionChange(selectedItems) {
      // Optional: Handle selection changes
      console.log('Selection changed:', selectedItems);
    },

    toggleVisitDetails(item) {
      const accountId = item.account_id;
      const index = this.expandedRows.indexOf(accountId);

      if (index > -1) {
        this.expandedRows.splice(index, 1);
      } else {
        this.expandedRows.push(accountId);
      }
    },

    getVisitDetails(accountId) {
      const account = this.reportData.find(item => item.account_id === accountId);
      return account ? account.visit_locations : [];
    },

    getAccountName(accountId) {
      const account = this.reportData.find(item => item.account_id === accountId);
      return account ? account.account_name : 'Unknown Account';
    },

    getDistanceClass(distance) {
      if (!distance) return 'text-muted';
      if (distance > this.filterData.distance_threshold) return 'text-danger font-weight-bold';
      if (distance > this.filterData.distance_threshold * 0.8) return 'text-warning';
      return 'text-success';
    },

    showOnMap(item) {
      if (item.registered_location && item.visit_locations) {
        const positions = [
          {
            lat: Number(item.registered_location.latitude),
            lng: Number(item.registered_location.longitude),
            title: 'Registered Location',
            type: 'registered'
          },
          ...item.visit_locations.map(visit => ({
            lat: Number(visit.latitude),
            lng: Number(visit.longitude),
            title: `Visit ${visit.visit_id} - ${visit.visit_date}`,
            type: 'visit'
          }))
        ];

        this.$root.$map(`Location Analysis for ${item.account_name}`, positions);
      }
    },

    print() {
      this.$htmlToPaper("print");
    },

    async download() {
      if (!this.shouldProceedWithLargeDataset(this.reportData.length, 'Excel export')) {
        return;
      }

      if (!this.initializeLargeDatasetProcessing('Excel Export')) {
        return;
      }

      try {
        this.isLoading = true;
        this.updateLargeDatasetProgress(10, 'Preparing Excel export...');

        // For very large datasets (>10k records), use chunked processing
        if (this.reportData.length > 10000) {
          await this.downloadLargeDataset();
        } else {
          this.updateLargeDatasetProgress(50, 'Generating Excel file...');
          this.downloadXlsx(this.exportData, `${this.reportName}.xlsx`);
          this.updateLargeDatasetProgress(100, 'Excel export complete');
        }

        this.flash('Excel export completed successfully', 'success');
        this.$emit("downloaded");

      } catch (error) {
        this.handleLargeDatasetError(error, 'Excel export');
      } finally {
        this.finalizeLargeDatasetProcessing();
        this.isLoading = false;
      }
    },

    async downloadLargeDataset() {
      try {
        // Process data in chunks to prevent memory issues
        const chunkSize = 5000;
        const chunks = [];

        this.updateLargeDatasetProgress(20, 'Splitting data into chunks...');

        for (let i = 0; i < this.exportData.length; i += chunkSize) {
          chunks.push(this.exportData.slice(i, i + chunkSize));
        }

        // Create separate files for very large datasets to prevent browser crashes
        if (chunks.length > 1) {
          this.updateLargeDatasetProgress(30, `Preparing ${chunks.length} Excel files...`);

          for (let i = 0; i < chunks.length; i++) {
            const fileName = `${this.reportName}_Part${i + 1}_of_${chunks.length}.xlsx`;

            this.updateLargeDatasetProgress(
              30 + (60 * (i + 1) / chunks.length),
              `Generating file ${i + 1} of ${chunks.length}...`
            );

            this.downloadXlsx(chunks[i], fileName);

            // Add small delay between downloads to prevent browser overload
            await new Promise(resolve => setTimeout(resolve, 500));
          }

          this.flash(`Dataset exported in ${chunks.length} files due to size`, 'success');
        } else {
          this.updateLargeDatasetProgress(50, 'Generating single Excel file...');
          this.downloadXlsx(this.exportData, `${this.reportName}.xlsx`);
        }

        this.updateLargeDatasetProgress(100, 'Excel export complete');

      } catch (error) {
        throw new Error(`Large dataset export failed: ${error.message}`);
      }
    },

    async downloadCsv() {
      try {
        // Show loading state for large datasets
        if (this.reportData.length > 1000) {
          this.isLoading = true;
          this.$toast.info('Preparing CSV export for large dataset...', {
            duration: 3000
          });
        }

        // For very large datasets, use chunked processing
        if (this.reportData.length > 10000) {
          await this.downloadLargeDatasetCsv();
        } else {
          this.downloadXlsx(this.exportData, `${this.reportName}.csv`);
        }

        this.$emit("downloaded");
      } catch (error) {
        this.showErrorMessage(error);
      } finally {
        this.isLoading = false;
      }
    },

    async downloadLargeDatasetCsv() {
      const chunkSize = 10000; // CSV can handle larger chunks
      const chunks = [];

      for (let i = 0; i < this.exportData.length; i += chunkSize) {
        chunks.push(this.exportData.slice(i, i + chunkSize));
      }

      if (chunks.length > 1) {
        for (let i = 0; i < chunks.length; i++) {
          const fileName = `${this.reportName}_Part${i + 1}_of_${chunks.length}.csv`;
          this.downloadXlsx(chunks[i], fileName);

          await new Promise(resolve => setTimeout(resolve, 300));
        }

        this.$toast.success(`CSV exported in ${chunks.length} files due to size`, {
          duration: 5000
        });
      } else {
        this.downloadXlsx(this.exportData, `${this.reportName}.csv`);
      }
    },

    createPDF() {
      const pdfName = "LocationDiscrepancyAnalysisReport";
      const columns = [
        {title: "Account ID", dataKey: "account_id"},
        {title: "Account Name", dataKey: "account_name"},
        {title: "Account Code", dataKey: "account_code"},
        {title: "Address", dataKey: "account_address"},
        {title: "Registered Location", dataKey: "registered_location"},
        {title: "Visits Count", dataKey: "visits_count"},
        {title: "Has Discrepancies", dataKey: "has_discrepancies"},
        {title: "Max Distance", dataKey: "max_distance_from_registered_location"},
        {title: "Threshold Used", dataKey: "distance_threshold_used"},
      ];

      const body = this.exportData;
      const doc = new jsPDF({filters: ["ASCIIHexEncode"]});

      doc.addFileToVFS("Amiri-Regular.ttf", Amiri);
      doc.addFont("Amiri-Regular.ttf", "Amiri", "normal");
      doc.setFont("Amiri");
      doc.setFontSize(10);

      // Add title
      doc.setFontSize(16);
      doc.text("Location Discrepancy Analysis Report", 14, 20);
      doc.setFontSize(12);
      doc.text(`Period: ${this.filterData.from_date} to ${this.filterData.to_date}`, 14, 30);
      doc.text(`Distance Threshold: ${this.filterData.distance_threshold}m`, 14, 40);

      doc.autoTable({
        columns,
        body,
        margin: {top: 50},
        showHead: "firstPage",
        styles: {
          lineColor: "#c7c7c7",
          lineWidth: 0,
          cellPadding: 2,
          font: "Amiri",
        },
      });

      doc.save(pdfName + ".pdf");
    },
  },
};
</script>

<style scoped>
.table-class > thead > tr > th {
  background-color: #005cc8;
  color: white;
  text-align: center;
  position: sticky;
  top: 0;
}

.badge {
  font-size: 0.75em;
}

.card {
  margin-bottom: 1rem;
}

.card-body h3 {
  margin-bottom: 0.5rem;
}

.text-danger.font-weight-bold {
  font-weight: 700 !important;
}
</style>
