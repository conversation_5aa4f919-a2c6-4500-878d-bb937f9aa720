<template>
  <c-card>
    <c-card-header>Location Discrepancy Analysis Report</c-card-header>
    <c-card-body>
      <c-tabs>
        <c-tab active>
          <template slot="title">
            <c-icon class="custom_icon" name="cil-location-pin" /> Main Filters
          </template>
          <c-card>
            <c-card-body>
              <div class="row">
                <div class="col-lg-3 col-md-3 col-sm-8">
                  <c-input
                    label="From Date"
                    type="date"
                    placeholder="From Date"
                    v-model="from_date"
                  ></c-input>
                </div>

                <div class="col-lg-3 col-md-3 col-sm-8">
                  <c-input
                    label="To Date"
                    type="date"
                    placeholder="To Date"
                    v-model="to_date"
                    @input="getAllData"
                  ></c-input>
                </div>

                <div class="col-lg-3 col-md-3 col-sm-8">
                  <div class="form-group">
                    <label>
                      <strong>Distance Threshold (meters)</strong>
                    </label>
                      <c-input
                        type="number"
                        v-model.number="distance_threshold"
                        placeholder="1000"
                        min="0"
                        step="100"
                      />
                  </div>
                </div>

                <div class="col-lg-3 col-md-3 col-sm-8">
                  <div class="form-group">
                    <label>
                      <strong>Recent Visits Limit</strong>
                    </label>
                      <c-input
                        type="number"
                        v-model.number="recent_visits_limit"
                        placeholder="5"
                        min="1"
                        max="50"
                      />
                  </div>
                </div>
              </div>
            </c-card-body>
          </c-card>
        </c-tab>
        <c-tab>
          <template slot="title">
            <c-icon class="custom_icon" name="cil-chart-pie" /> Account Selection
          </template>
          <c-card>
            <c-card-body>
              <div class="row">
                <div class="col-lg-4 col-md-4 col-sm-8">
                  <div class="form-group">
                    <label>
                      <strong>Lines</strong>
                    </label>
                    <select-all-checkbox
                      :items="lines"
                      v-model="checkAllLines"
                    />

                    <v-select
                      v-model="line_ids"
                      :options="lines"
                      label="name"
                      :value="0"
                      :reduce="(line) => line.id"
                      placeholder="Select Lines"
                      multiple
                      @input="getLineData"
                    />
                  </div>
                </div>

                <div class="col-lg-4 col-md-4 col-sm-8">
                  <div class="form-group">
                    <label>
                      <strong>Divisions</strong>
                    </label>
                    <select-all-checkbox
                      :items="divisions"
                      v-model="checkAllDivisions"
                    />

                    <v-select
                      v-model="div_ids"
                      :options="divisions"
                      label="name"
                      :reduce="(division) => division.id"
                      placeholder="Select Divisions"
                      multiple
                      :filterable="true"
                    />
                  </div>
                </div>
              </div>
            </c-card-body>
          </c-card>
        </c-tab>
      </c-tabs>
    </c-card-body>
    <c-card-footer>
      <c-button
        color="primary"
        class="text-white"
        @click="show"
        style="float: right"
        :disabled="isLoading"
      >
        <c-spinner v-if="isLoading" size="sm" class="mr-2" />
        {{ isLoading ? 'Loading...' : 'Generate Report' }}
      </c-button>
    </c-card-footer>
  </c-card>
</template>

<script>
import moment from "moment";
import vSelect from "vue-select";
import "vue-select/dist/vue-select.css";
import SelectAllCheckbox from "../../common/SelectAllCheckBox.vue";

export default {
  components: {
    SelectAllCheckbox,
    vSelect,
  },
  emits: ["getLocationDiscrepancy"],
  data() {
    return {
      // Filter values
      from_date: moment().startOf("month").format("YYYY-MM-DD"),
      to_date: moment().endOf("month").format("YYYY-MM-DD"),
      distance_threshold: 1000,
      recent_visits_limit: 5,

      // Account selection
      div_ids: [],
      line_ids: [],

      // Data arrays
      divisions: [],
      lines: [],

      // Checkbox states
      checkAllDivisions: false,
      checkAllLines: false,
      allAccountTypes: true,

      // Loading state
      isLoading: false,
    };
  },
  methods: {
    initialize() {
      this.isLoading = true;
      axios
        .post("/api/get-lines-with-dates", {
          from: this.from_date,
          to: this.to_date,
          data: ['lines', 'divisions']
        })
        .then((response) => {
          this.lines = response.data.data.lines;
          this.divisions = response.data.data.divisions || [];
        })
        .catch((error) => {
          this.showErrorMessage(error);
        })
        .finally(() => {
          this.isLoading = false;
        });
    },

    getAllData() {
      this.line_ids = [];
      this.div_ids = [];
      this.initialize();
    },

    getLineData() {
      if (this.line_ids.length === 0) {
        this.divisions = [];
        return;
      }

      this.isLoading = true;
      axios
        .post(`/api/get-lines-data-with-dates`, {
          lines: this.line_ids,
          from: this.from_date,
          to: this.to_date,
          data: ['divisions']
        })
        .then((response) => {
          this.divisions = response.data.data.divisions || [];
        })
        .catch((error) => {
          this.showErrorMessage(error);
        })
        .finally(() => {
          this.isLoading = false;
        });
    },

    checkAllLine() {
      if (this.checkAllLines) {
        this.line_ids = this.lines.map((item) => item.id);
      } else {
        this.line_ids = [];
      }
      this.getLineData();
    },
    show() {
      const filters = {
        from_date: this.from_date,
        to_date: this.to_date,
        distance_threshold: this.distance_threshold,
        recent_visits_limit: this.recent_visits_limit,
        div_ids: this.div_ids,
        line_ids: this.line_ids,
      };

      this.$emit("getLocationDiscrepancy", { filters });
    },
  },
  created() {
    this.initialize();
  },
};
</script>
