<template>
  <div class="virtual-column-example">
    <h2>Vue2DataTable Virtual Column Formatter Example</h2>
    
    <p class="description">
      This example demonstrates the new virtual column formatter feature that allows creating 
      computed columns without modifying the original data structure.
    </p>

    <!-- Example 1: Basic Virtual Columns -->
    <div class="example-section">
      <h3>Example 1: Basic Virtual Columns</h3>
      <Vue2DataTable
        :columns="basicColumns"
        :data-source="sampleData"
        :height="400"
        :searchable="true"
        :sortable="true"
        :paginated="true"
        :selectable="true"
        :show-total-bar="true"
      />
    </div>

    <!-- Example 2: Advanced Virtual Columns -->
    <div class="example-section">
      <h3>Example 2: Advanced Virtual Columns with Complex Formatting</h3>
      <Vue2DataTable
        :columns="advancedColumns"
        :data-source="sampleData"
        :height="400"
        :searchable="true"
        :sortable="true"
        :paginated="true"
        :selectable="true"
        :show-total-bar="true"
      />
    </div>

    <!-- Code Examples -->
    <div class="code-examples">
      <h3>Code Examples</h3>
      
      <div class="code-block">
        <h4>Basic Virtual Column Definition:</h4>
        <pre><code>{{basicColumnCode}}</code></pre>
      </div>

      <div class="code-block">
        <h4>Advanced Virtual Column Definition:</h4>
        <pre><code>{{advancedColumnCode}}</code></pre>
      </div>
    </div>
  </div>
</template>

<script>
import Vue2DataTable from '../components/core/Vue2DataTable.vue'

export default {
  name: 'VirtualColumnExample',
  
  components: {
    Vue2DataTable
  },

  data() {
    return {
      // Sample data with separate fields
      sampleData: [
        {
          id: 1,
          first_name: 'John',
          last_name: 'Doe',
          email: '<EMAIL>',
          age: 30,
          salary: 75000,
          department: 'Engineering',
          hire_date: '2020-01-15',
          active: true,
          skills: ['JavaScript', 'Vue.js', 'Node.js']
        },
        {
          id: 2,
          first_name: 'Jane',
          last_name: 'Smith',
          email: '<EMAIL>',
          age: 28,
          salary: 82000,
          department: 'Design',
          hire_date: '2019-03-22',
          active: true,
          skills: ['UI/UX', 'Figma', 'Adobe Creative Suite']
        },
        {
          id: 3,
          first_name: 'Bob',
          last_name: 'Johnson',
          email: '<EMAIL>',
          age: 35,
          salary: 95000,
          department: 'Engineering',
          hire_date: '2018-07-10',
          active: false,
          skills: ['Python', 'Django', 'PostgreSQL']
        },
        {
          id: 4,
          first_name: 'Alice',
          last_name: 'Williams',
          email: '<EMAIL>',
          age: 32,
          salary: 88000,
          department: 'Marketing',
          hire_date: '2021-05-18',
          active: true,
          skills: ['Digital Marketing', 'Analytics', 'SEO']
        },
        {
          id: 5,
          first_name: 'Charlie',
          last_name: 'Brown',
          email: '<EMAIL>',
          age: 29,
          salary: 72000,
          department: 'Sales',
          hire_date: '2020-11-03',
          active: true,
          skills: ['Sales', 'CRM', 'Negotiation']
        }
      ],

      // Basic columns with virtual formatters
      basicColumns: [
        {
          key: 'id',
          label: 'ID',
          width: '80px',
          type: 'number'
        },
        {
          key: 'full_name',
          label: 'Full Name',
          virtual: true,
          formatter: (item) => `${item.first_name} ${item.last_name}`,
          width: '200px'
        },
        {
          key: 'email',
          label: 'Email',
          width: '250px'
        },
        {
          key: 'age',
          label: 'Age',
          width: '80px',
          type: 'number'
        },
        {
          key: 'department',
          label: 'Department',
          width: '150px'
        }
      ],

      // Advanced columns with complex virtual formatters
      advancedColumns: [
        {
          key: 'employee_info',
          label: 'Employee Info',
          virtual: true,
          formatter: (item) => {
            const status = item.active ? '🟢 Active' : '🔴 Inactive'
            return `${item.first_name} ${item.last_name} (${status})`
          },
          width: '250px'
        },
        {
          key: 'salary_formatted',
          label: 'Salary',
          virtual: true,
          formatter: (item) => {
            return new Intl.NumberFormat('en-US', {
              style: 'currency',
              currency: 'USD'
            }).format(item.salary)
          },
          width: '120px',
          type: 'number'
        },
        {
          key: 'experience',
          label: 'Experience',
          virtual: true,
          formatter: (item) => {
            const hireDate = new Date(item.hire_date)
            const now = new Date()
            const years = Math.floor((now - hireDate) / (365.25 * 24 * 60 * 60 * 1000))
            return `${years} year${years !== 1 ? 's' : ''}`
          },
          width: '120px'
        },
        {
          key: 'skills_summary',
          label: 'Skills',
          virtual: true,
          formatter: (item) => {
            if (!item.skills || item.skills.length === 0) return 'No skills listed'
            if (item.skills.length <= 2) return item.skills.join(', ')
            return `${item.skills.slice(0, 2).join(', ')} +${item.skills.length - 2} more`
          },
          width: '200px'
        },
        {
          key: 'performance_score',
          label: 'Performance',
          virtual: true,
          formatter: (item) => {
            // Mock performance calculation based on salary and experience
            const baseScore = Math.min(item.salary / 1000, 100)
            const experienceBonus = this.getExperienceYears(item.hire_date) * 5
            const score = Math.min(baseScore + experienceBonus, 100)
            
            let rating = '⭐'
            if (score >= 90) rating = '⭐⭐⭐⭐⭐'
            else if (score >= 80) rating = '⭐⭐⭐⭐'
            else if (score >= 70) rating = '⭐⭐⭐'
            else if (score >= 60) rating = '⭐⭐'
            
            return `${Math.round(score)}% ${rating}`
          },
          width: '150px'
        }
      ]
    }
  },

  computed: {
    basicColumnCode() {
      return `{
  key: 'full_name',
  label: 'Full Name',
  virtual: true,
  formatter: (item) => \`\${item.first_name} \${item.last_name}\`,
  width: '200px'
}`
    },

    advancedColumnCode() {
      return `{
  key: 'salary_formatted',
  label: 'Salary',
  virtual: true,
  formatter: (item) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(item.salary)
  },
  width: '120px',
  type: 'number'
}`
    }
  },

  methods: {
    getExperienceYears(hireDate) {
      const hire = new Date(hireDate)
      const now = new Date()
      return Math.floor((now - hire) / (365.25 * 24 * 60 * 60 * 1000))
    }
  }
}
</script>

<style lang="scss" scoped>
.virtual-column-example {
  padding: 24px;
  max-width: 1200px;
  margin: 0 auto;
}

.description {
  background: #f8fafc;
  padding: 16px;
  border-radius: 8px;
  border-left: 4px solid #3b82f6;
  margin-bottom: 32px;
  color: #374151;
  font-size: 14px;
  line-height: 1.6;
}

.example-section {
  margin-bottom: 48px;
  
  h3 {
    color: #1f2937;
    margin-bottom: 16px;
    font-size: 18px;
    font-weight: 600;
  }
}

.code-examples {
  margin-top: 48px;
  
  h3 {
    color: #1f2937;
    margin-bottom: 24px;
    font-size: 20px;
    font-weight: 600;
  }
}

.code-block {
  margin-bottom: 24px;
  
  h4 {
    color: #374151;
    margin-bottom: 8px;
    font-size: 14px;
    font-weight: 600;
  }
  
  pre {
    background: #1f2937;
    color: #f9fafb;
    padding: 16px;
    border-radius: 8px;
    overflow-x: auto;
    font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Fira Code', monospace;
    font-size: 13px;
    line-height: 1.5;
    
    code {
      background: none;
      color: inherit;
      padding: 0;
    }
  }
}

h2 {
  color: #1f2937;
  margin-bottom: 24px;
  font-size: 24px;
  font-weight: 700;
}
</style>
