# Virtual Column Formatters

The Vue2DataTable component now supports **Virtual Column Formatters**, a powerful feature that allows you to create computed columns without modifying the original data structure. This feature enables you to display formatted, combined, or calculated values while maintaining the integrity of your source data.

## Overview

Virtual columns are columns that don't exist in your original dataset but are computed on-the-fly using formatter functions. They integrate seamlessly with all existing table features including:

- ✅ **Searching** - Virtual columns are fully searchable
- ✅ **Sorting** - Sort by virtual column values
- ✅ **Filtering** - Apply filters to virtual columns
- ✅ **Selection** - Works with row selection
- ✅ **Performance** - Optimized for large datasets

## Basic Usage

### Simple Virtual Column

```javascript
{
  key: 'full_name',           // Unique identifier for the column
  label: 'Full Name',         // Display name in header
  virtual: true,              // Mark as virtual column
  formatter: (item) => `${item.first_name} ${item.last_name}`,
  width: '200px'
}
```

### Column Configuration

| Property | Type | Required | Description |
|----------|------|----------|-------------|
| `key` | String | ✅ | Unique identifier for the column |
| `label` | String | ✅ | Display name for the column header |
| `virtual` | Boolean | ✅ | Must be `true` for virtual columns |
| `formatter` | Function | ✅ | Function that receives the row item and returns the display value |
| `width` | String/Number | ❌ | Column width (e.g., '200px', 150) |
| `type` | String | ❌ | Data type for sorting ('text', 'number', 'date', 'boolean') |
| `align` | String | ❌ | Text alignment ('left', 'center', 'right') |
| `sortable` | Boolean | ❌ | Whether the column is sortable (default: true) |
| `searchable` | Boolean | ❌ | Whether the column is searchable (default: true) |

## Formatter Function

The formatter function is the heart of virtual columns. It receives the complete row object as its parameter and should return the value to display.

### Function Signature

```javascript
formatter: (item) => {
  // item: Complete row object from your data
  // Return: Value to display in the cell
  return computedValue
}
```

### Examples

#### 1. Combining Fields
```javascript
{
  key: 'full_name',
  label: 'Full Name',
  virtual: true,
  formatter: (item) => `${item.first_name} ${item.last_name}`
}
```

#### 2. Currency Formatting
```javascript
{
  key: 'salary_formatted',
  label: 'Salary',
  virtual: true,
  formatter: (item) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(item.salary)
  },
  type: 'number'
}
```

#### 3. Date Calculations
```javascript
{
  key: 'experience',
  label: 'Experience',
  virtual: true,
  formatter: (item) => {
    const hireDate = new Date(item.hire_date)
    const now = new Date()
    const years = Math.floor((now - hireDate) / (365.25 * 24 * 60 * 60 * 1000))
    return `${years} year${years !== 1 ? 's' : ''}`
  }
}
```

#### 4. Conditional Formatting
```javascript
{
  key: 'status_display',
  label: 'Status',
  virtual: true,
  formatter: (item) => {
    if (item.active) {
      return '🟢 Active'
    } else {
      return '🔴 Inactive'
    }
  }
}
```

#### 5. Array Processing
```javascript
{
  key: 'skills_summary',
  label: 'Skills',
  virtual: true,
  formatter: (item) => {
    if (!item.skills || item.skills.length === 0) {
      return 'No skills listed'
    }
    if (item.skills.length <= 2) {
      return item.skills.join(', ')
    }
    return `${item.skills.slice(0, 2).join(', ')} +${item.skills.length - 2} more`
  }
}
```

## Advanced Features

### HTML Content

Virtual columns support HTML content for rich formatting:

```javascript
{
  key: 'profile_card',
  label: 'Profile',
  virtual: true,
  formatter: (item) => {
    const avatar = item.avatar || '/default-avatar.png'
    const status = item.active ? 'active' : 'inactive'
    return `
      <div class="profile-card">
        <img src="${avatar}" alt="Avatar" class="avatar" />
        <div class="info">
          <strong>${item.first_name} ${item.last_name}</strong>
          <span class="status ${status}">${status}</span>
        </div>
      </div>
    `
  }
}
```

### Complex Calculations

```javascript
{
  key: 'performance_score',
  label: 'Performance',
  virtual: true,
  formatter: (item) => {
    // Complex calculation based on multiple factors
    const baseScore = Math.min(item.salary / 1000, 100)
    const experienceBonus = getExperienceYears(item.hire_date) * 5
    const departmentMultiplier = getDepartmentMultiplier(item.department)
    
    const score = Math.min(
      (baseScore + experienceBonus) * departmentMultiplier, 
      100
    )
    
    const stars = '⭐'.repeat(Math.ceil(score / 20))
    return `${Math.round(score)}% ${stars}`
  }
}
```

## Integration with Table Features

### Searching

Virtual columns are automatically included in search operations. The search function will call the formatter to get the searchable value:

```javascript
// This virtual column will be searchable
{
  key: 'searchable_name',
  label: 'Name',
  virtual: true,
  formatter: (item) => `${item.first_name} ${item.last_name}`,
  searchable: true  // Default: true
}

// Exclude from search
{
  key: 'display_only',
  label: 'Display Only',
  virtual: true,
  formatter: (item) => item.computed_value,
  searchable: false
}
```

### Sorting

Virtual columns support sorting based on their formatted values:

```javascript
{
  key: 'sortable_salary',
  label: 'Salary',
  virtual: true,
  formatter: (item) => `$${item.salary.toLocaleString()}`,
  type: 'number',  // Helps with proper sorting
  sortable: true   // Default: true
}
```

### Performance Considerations

1. **Formatter Efficiency**: Keep formatter functions lightweight as they're called for every row
2. **Caching**: Consider caching expensive calculations outside the formatter
3. **Type Hints**: Use the `type` property to help with sorting performance

```javascript
// Good: Simple and fast
formatter: (item) => `${item.first_name} ${item.last_name}`

// Consider optimization: Complex calculation
formatter: (item) => {
  // Cache expensive operations
  if (!item._cachedScore) {
    item._cachedScore = calculateComplexScore(item)
  }
  return item._cachedScore
}
```

## Migration from Existing Code

### Before (Manual Data Transformation)
```javascript
// Old approach: Modify data before passing to table
const processedData = rawData.map(item => ({
  ...item,
  full_name: `${item.first_name} ${item.last_name}`,
  formatted_salary: `$${item.salary.toLocaleString()}`
}))
```

### After (Virtual Columns)
```javascript
// New approach: Keep data pure, use virtual columns
const columns = [
  {
    key: 'full_name',
    label: 'Full Name',
    virtual: true,
    formatter: (item) => `${item.first_name} ${item.last_name}`
  },
  {
    key: 'formatted_salary',
    label: 'Salary',
    virtual: true,
    formatter: (item) => `$${item.salary.toLocaleString()}`,
    type: 'number'
  }
]
```

## Best Practices

1. **Keep Original Data Intact**: Never modify the source data structure
2. **Use Descriptive Keys**: Choose meaningful keys for virtual columns
3. **Optimize Formatters**: Keep formatter functions simple and fast
4. **Type Specification**: Specify the `type` property for better sorting
5. **Error Handling**: Handle edge cases in formatter functions
6. **Consistent Formatting**: Use consistent formatting patterns across similar columns

## Troubleshooting

### Common Issues

1. **Formatter Not Called**: Ensure `virtual: true` is set
2. **Search Not Working**: Check that `searchable` is not set to `false`
3. **Sorting Issues**: Specify the correct `type` for the data
4. **Performance Problems**: Optimize formatter functions for large datasets

### Debug Tips

```javascript
// Add logging to debug formatter issues
formatter: (item) => {
  console.log('Formatting item:', item)
  const result = `${item.first_name} ${item.last_name}`
  console.log('Formatted result:', result)
  return result
}
```
