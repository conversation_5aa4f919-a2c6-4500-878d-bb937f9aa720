/**
 * Search Mixin for Vue2 DataTable
 * Provides comprehensive search and filtering functionality
 */

import { debounceSearch } from '../utils/debounce.js'
import { performantSearch, globalPerformanceMonitor } from '../utils/performance.js'

export default {
  data() {
    return {
      // Search state
      internalSearchTerm: '',
      searchHistory: [],
      maxSearchHistory: 10,
      
      // Search configuration
      searchFields: [],
      searchMode: 'contains', // 'contains', 'startsWith', 'exact'
      caseSensitive: false,
      minSearchLength: 2,
      
      // Advanced filters
      activeFilters: {},
      filterOperators: {
        equals: '=',
        notEquals: '!=',
        contains: 'contains',
        startsWith: 'startsWith',
        endsWith: 'endsWith',
        greaterThan: '>',
        lessThan: '<',
        greaterThanOrEqual: '>=',
        lessThanOrEqual: '<=',
        between: 'between',
        in: 'in',
        notIn: 'notIn'
      },
      
      // Search performance
      searchDebounceDelay: 300,
      isSearching: false,
      searchStartTime: 0,
      searchDuration: 0,
      
      // Highlighting
      highlightMatches: true,
      highlightClass: 'search-highlight',
      
      // Saved searches
      savedSearches: [],
      maxSavedSearches: 20
    }
  },

  computed: {
    /**
     * Get searchable columns with virtual column support
     */
    searchableColumns() {
      if (this.searchFields.length > 0) {
        return this.searchFields
      }

      return (this.processedColumns || [])
        .filter(col => col.searchable !== false)
        .map(col => ({
          key: col.key,
          virtual: col.virtual || false,
          formatter: col.formatter || null
        }))
    },

    /**
     * Get filtered items based on search and filters
     */
    filteredItems() {
      const endMeasurement = globalPerformanceMonitor.startMeasurement('filterItems')
      
      try {
        let items = this.items || []
        
        // Apply search filter
        if (this.internalSearchTerm && this.internalSearchTerm.length >= this.minSearchLength) {
          items = this.performSearch(items, this.internalSearchTerm)
        }
        
        // Apply advanced filters
        items = this.applyAdvancedFilters(items)
        
        return items
      } finally {
        endMeasurement()
      }
    },

    /**
     * Get search statistics
     */
    searchStats() {
      const totalItems = this.items ? this.items.length : 0
      const filteredCount = this.filteredItems ? this.filteredItems.length : 0
      
      return {
        total: totalItems,
        filtered: filteredCount,
        hidden: totalItems - filteredCount,
        hasFilter: this.hasActiveSearch || this.hasActiveFilters,
        searchTerm: this.internalSearchTerm,
        duration: this.searchDuration
      }
    },

    /**
     * Check if there's an active search
     */
    hasActiveSearch() {
      return this.internalSearchTerm && this.internalSearchTerm.length >= this.minSearchLength
    },

    /**
     * Check if there are active filters
     */
    hasActiveFilters() {
      return Object.keys(this.activeFilters).length > 0
    },

    /**
     * Get search suggestions based on data with virtual column support
     */
    searchSuggestions() {
      if (!this.internalSearchTerm || this.internalSearchTerm.length < 2) {
        return []
      }

      const suggestions = new Set()
      const term = this.internalSearchTerm.toLowerCase()
      const items = this.items || []

      items.forEach(item => {
        this.searchableColumns.forEach(column => {
          let value

          // Handle virtual columns
          if (typeof column === 'object' && column.virtual && column.formatter) {
            value = column.formatter(item)
          } else {
            // Handle regular columns (backward compatibility)
            const fieldKey = typeof column === 'object' ? column.key : column
            value = this.getNestedValue(item, fieldKey)
          }

          if (value && typeof value === 'string') {
            const lowerValue = value.toLowerCase()
            if (lowerValue.includes(term) && lowerValue !== term) {
              suggestions.add(value)
            }
          }
        })
      })

      return Array.from(suggestions).slice(0, 10)
    }
  },

  created() {
    this.initializeSearch()
  },

  methods: {
    /**
     * Initialize search functionality
     */
    initializeSearch() {
      // Create debounced search function
      this.debouncedSearch = debounceSearch(
        this.performSearchOperation,
        this.searchDebounceDelay,
        this.minSearchLength
      )

      // Load saved searches from localStorage
      this.loadSavedSearches()
    },

    /**
     * Perform search operation
     */
    performSearchOperation(searchTerm) {
      this.isSearching = true
      this.searchStartTime = performance.now()

      this.$nextTick(() => {
        this.searchDuration = performance.now() - this.searchStartTime
        this.isSearching = false

        // Add to search history
        if (searchTerm && searchTerm.length >= this.minSearchLength) {
          this.addToSearchHistory(searchTerm)
        }

        // Emit search event
        this.$emit('search', {
          searchTerm,
          results: this.filteredItems,
          stats: this.searchStats
        })
      })
    },

    /**
     * Perform search on items
     */
    performSearch(items, searchTerm) {
      if (!searchTerm || searchTerm.length < this.minSearchLength) {
        return items
      }

      return performantSearch(items, searchTerm, this.searchableColumns, {
        caseSensitive: this.caseSensitive,
        exactMatch: this.searchMode === 'exact',
        minLength: this.minSearchLength
      })
    },

    /**
     * Apply advanced filters to items
     */
    applyAdvancedFilters(items) {
      if (!this.hasActiveFilters) {
        return items
      }

      return items.filter(item => {
        return Object.entries(this.activeFilters).every(([field, filter]) => {
          return this.evaluateFilter(item, field, filter)
        })
      })
    },

    /**
     * Evaluate a single filter against an item
     */
    evaluateFilter(item, field, filter) {
      const value = this.getNestedValue(item, field)
      const { operator, value: filterValue, caseSensitive = false } = filter

      if (value === null || value === undefined) {
        return operator === 'notEquals' || operator === 'notIn'
      }

      const itemValue = caseSensitive ? String(value) : String(value).toLowerCase()
      const compareValue = caseSensitive ? String(filterValue) : String(filterValue).toLowerCase()

      switch (operator) {
        case 'equals':
          return itemValue === compareValue
        case 'notEquals':
          return itemValue !== compareValue
        case 'contains':
          return itemValue.includes(compareValue)
        case 'startsWith':
          return itemValue.startsWith(compareValue)
        case 'endsWith':
          return itemValue.endsWith(compareValue)
        case 'greaterThan':
          return Number(value) > Number(filterValue)
        case 'lessThan':
          return Number(value) < Number(filterValue)
        case 'greaterThanOrEqual':
          return Number(value) >= Number(filterValue)
        case 'lessThanOrEqual':
          return Number(value) <= Number(filterValue)
        case 'between':
          const [min, max] = filterValue
          return Number(value) >= Number(min) && Number(value) <= Number(max)
        case 'in':
          return Array.isArray(filterValue) && filterValue.includes(value)
        case 'notIn':
          return Array.isArray(filterValue) && !filterValue.includes(value)
        default:
          return true
      }
    },

    /**
     * Add filter
     */
    addFilter(field, operator, value, options = {}) {
      this.activeFilters = {
        ...this.activeFilters,
        [field]: {
          operator,
          value,
          caseSensitive: options.caseSensitive || false,
          label: options.label || `${field} ${operator} ${value}`
        }
      }

      this.$emit('filter-change', {
        filters: this.activeFilters,
        field,
        operator,
        value
      })
    },

    /**
     * Remove filter
     */
    removeFilter(field) {
      const newFilters = { ...this.activeFilters }
      delete newFilters[field]
      this.activeFilters = newFilters

      this.$emit('filter-remove', {
        filters: this.activeFilters,
        removedField: field
      })
    },

    /**
     * Clear all filters
     */
    clearFilters() {
      this.activeFilters = {}
      this.$emit('filters-clear')
    },

    /**
     * Clear search
     */
    clearSearch() {
      this.internalSearchTerm = ''
      this.$emit('search-clear')
    },

    /**
     * Clear all (search and filters)
     */
    clearAll() {
      this.clearSearch()
      this.clearFilters()
      this.$emit('clear-all')
    },

    /**
     * Add to search history
     */
    addToSearchHistory(searchTerm) {
      // Remove if already exists
      const index = this.searchHistory.indexOf(searchTerm)
      if (index > -1) {
        this.searchHistory.splice(index, 1)
      }

      // Add to beginning
      this.searchHistory.unshift(searchTerm)

      // Limit history size
      if (this.searchHistory.length > this.maxSearchHistory) {
        this.searchHistory = this.searchHistory.slice(0, this.maxSearchHistory)
      }
    },

    /**
     * Save current search
     */
    saveCurrentSearch(name) {
      if (!name || !this.internalSearchTerm) return

      const search = {
        name,
        searchTerm: this.internalSearchTerm,
        filters: { ...this.activeFilters },
        timestamp: Date.now()
      }

      // Remove existing search with same name
      this.savedSearches = this.savedSearches.filter(s => s.name !== name)

      // Add new search
      this.savedSearches.unshift(search)

      // Limit saved searches
      if (this.savedSearches.length > this.maxSavedSearches) {
        this.savedSearches = this.savedSearches.slice(0, this.maxSavedSearches)
      }

      this.saveSavedSearches()
      this.$emit('search-saved', search)
    },

    /**
     * Load saved search
     */
    loadSavedSearch(search) {
      this.internalSearchTerm = search.searchTerm || ''
      this.activeFilters = { ...search.filters } || {}
      this.$emit('search-loaded', search)
    },

    /**
     * Delete saved search
     */
    deleteSavedSearch(name) {
      this.savedSearches = this.savedSearches.filter(s => s.name !== name)
      this.saveSavedSearches()
      this.$emit('search-deleted', name)
    },

    /**
     * Get nested value from object
     */
    getNestedValue(obj, path) {
      return path.split('.').reduce((current, key) => {
        return current && current[key] !== undefined ? current[key] : undefined
      }, obj)
    },

    /**
     * Highlight search matches in text
     */
    highlightSearchMatches(text, searchTerm) {
      if (!this.highlightMatches || !searchTerm || !text) {
        return text
      }

      const regex = new RegExp(`(${this.escapeRegex(searchTerm)})`, this.caseSensitive ? 'g' : 'gi')
      return String(text).replace(regex, `<span class="${this.highlightClass}">$1</span>`)
    },

    /**
     * Escape regex special characters
     */
    escapeRegex(string) {
      return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')
    },

    /**
     * Load saved searches from localStorage
     */
    loadSavedSearches() {
      try {
        const saved = localStorage.getItem('vue2-datatable-saved-searches')
        if (saved) {
          this.savedSearches = JSON.parse(saved)
        }
      } catch (error) {
        console.warn('Failed to load saved searches:', error)
      }
    },

    /**
     * Save searches to localStorage
     */
    saveSavedSearches() {
      try {
        localStorage.setItem('vue2-datatable-saved-searches', JSON.stringify(this.savedSearches))
      } catch (error) {
        console.warn('Failed to save searches:', error)
      }
    }
  },

  watch: {
    searchableColumns: {
      handler() {
        // Recalculate search when searchable columns change
        if (this.hasActiveSearch) {
          this.debouncedSearch(this.internalSearchTerm)
        }
      },
      immediate: true
    }
  }
}
