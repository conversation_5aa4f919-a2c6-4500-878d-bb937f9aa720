/**
 * Virtual Scrolling Mixin for Vue2 DataTable - FIXED VERSION
 * Provides virtual scrolling functionality for handling large datasets
 */

import { throttlePerformance, createVirtualListCalculator } from '../utils/performance.js'

export default {
  data() {
    return {
      // Virtual scrolling state
      containerHeight: 600,
      containerWidth: 0,
      scrollTop: 0,
      scrollLeft: 0,

      // Visible range - ensure these are reactive
      startIndex: 0,
      endIndex: 0,
      visibleRowCount: 0,

      // Buffer and optimization
      overscan: 3,

      // Column virtualization
      columnVirtualizationEnabled: false,
      visibleColumnStartIndex: 0,
      visibleColumnEndIndex: 0,
      totalColumnsWidth: 0,

      // Performance tracking
      lastScrollTime: 0,
      scrollDirection: 'down',
      isScrolling: false,
      scrollTimeout: null,

      // Resize observer
      resizeObserver: null,

      // Virtual list calculator
      virtualListCalculator: null,

      // Scroll sensitivity controls (props are used instead of data properties)
      isWheelScrolling: false,
      wheelScrollTimeout: null,
      targetScrollTop: 0,
      targetScrollLeft: 0,
      scrollAnimationFrame: null,

      // Reactivity management
      _virtualScrollState: {
        revision: 0,
        lastCalculation: null,
        isUpdating: false
      }
    }
  },

  computed: {
    /**
     * Check if virtual scrolling should be enabled - ENHANCED WITH PERFORMANCE SAFEGUARDS
     */
    shouldUseVirtualScrolling() {
      if (this.virtualScrollDisabled) return false

      const itemCount = this.filteredItems ? this.filteredItems.length : 0

      // ENHANCED: Browser freeze prevention for extremely large datasets
      if (itemCount > 500000) {
        console.warn(`Vue2DataTable: Extremely large dataset (${itemCount} items). Consider server-side pagination.`)
        // Force virtual scrolling for very large datasets
        return true
      }

      if (this.virtualScrollEnabled === true) return true
      if (this.virtualScrollEnabled === false) return false

      const threshold = this.virtualScrollThreshold || 100

      // ENHANCED: Adaptive threshold based on browser performance
      const adaptiveThreshold = this.getAdaptiveThreshold(threshold)

      return itemCount >= adaptiveThreshold
    },

    /**
     * Get adaptive threshold based on browser performance
     */
    getAdaptiveThreshold(baseThreshold) {
      // Check browser performance capabilities
      const memoryInfo = performance.memory
      if (memoryInfo) {
        const usedMemory = memoryInfo.usedJSHeapSize / 1024 / 1024 // MB
        const totalMemory = memoryInfo.totalJSHeapSize / 1024 / 1024 // MB
        const memoryUsageRatio = usedMemory / totalMemory

        // Lower threshold if memory usage is high
        if (memoryUsageRatio > 0.8) {
          return Math.max(50, baseThreshold * 0.5)
        } else if (memoryUsageRatio > 0.6) {
          return Math.max(75, baseThreshold * 0.75)
        }
      }

      // Check if device seems to be mobile/low-power
      const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)
      if (isMobile) {
        return Math.max(50, baseThreshold * 0.5)
      }

      return baseThreshold
    },

    /**
     * Check if column virtualization should be enabled
     */
    shouldUseColumnVirtualization() {
      if (!this.shouldUseVirtualScrolling) return false
      
      const threshold = this.columnVirtualizationThreshold || 50
      const columnCount = this.processedColumns ? this.processedColumns.length : 0
      
      return this.columnVirtualizationEnabled || columnCount >= threshold
    },

    /**
     * Get visible items for virtual scrolling - ENHANCED WITH COMPREHENSIVE ERROR HANDLING
     */
    visibleItems() {
      try {
        // CRITICAL FIX: Ensure _virtualScrollState is initialized before accessing
        const virtualScrollState = this.ensureVirtualScrollState()

        // CRITICAL FIX: Use multiple reactive dependencies to ensure proper updates
        // Access all reactive properties that should trigger recalculation
        const revision = virtualScrollState.revision
        const currentStartIndex = this.startIndex || 0
        const currentEndIndex = this.endIndex || 0
        const currentScrollTop = this.scrollTop || 0
        const currentFilteredItems = this.filteredItems

        if (!this.shouldUseVirtualScrolling) {
          return this.paginatedItems || currentFilteredItems || []
        }

        const items = currentFilteredItems || []
        const totalItems = items.length

        if (totalItems === 0) {
          return []
        }

        // ENHANCED: More robust bounds checking
        const safeStartIndex = Math.max(0, Math.min(currentStartIndex, totalItems - 1))
        const safeEndIndex = Math.max(safeStartIndex, Math.min(currentEndIndex, totalItems - 1))

        // CRITICAL FIX: Ensure we slice correctly with proper bounds
        const sliceStart = safeStartIndex
        const sliceEnd = Math.min(safeEndIndex + 1, totalItems)

        // Validate slice parameters
        if (sliceStart >= totalItems || sliceEnd <= sliceStart) {
          if (process.env.NODE_ENV === 'development') {
            console.warn('Vue2DataTable: Invalid slice parameters', {
              sliceStart, sliceEnd, totalItems, safeStartIndex, safeEndIndex,
              virtualScrollState: virtualScrollState
            })
          }
          return []
        }

        const visibleSlice = items.slice(sliceStart, sliceEnd)

        // Enhanced debug logging
        if (process.env.NODE_ENV === 'development' && this.enablePerformanceMonitoring) {
          console.log('visibleItems calculation:', {
            totalItems,
            startIndex: currentStartIndex,
            endIndex: currentEndIndex,
            safeStartIndex,
            safeEndIndex,
            sliceStart,
            sliceEnd,
            resultLength: visibleSlice.length,
            scrollTop: currentScrollTop,
            containerHeight: this.containerHeight,
            revision,
            virtualScrollStateExists: !!virtualScrollState,
            timestamp: Date.now()
          })
        }

        return visibleSlice
      } catch (error) {
        console.error('Vue2DataTable: Error in visibleItems calculation:', error)

        // ENHANCED: Comprehensive fallback mechanism
        try {
          // Try to return filtered items or paginated items as fallback
          const fallbackItems = this.filteredItems || this.paginatedItems || []

          // If we have too many items, limit to prevent browser freeze
          if (fallbackItems.length > 1000) {
            console.warn('Vue2DataTable: Using limited fallback due to large dataset')
            return fallbackItems.slice(0, 100) // Show first 100 items as emergency fallback
          }

          return fallbackItems
        } catch (fallbackError) {
          console.error('Vue2DataTable: Fallback also failed:', fallbackError)
          // Ultimate fallback - return empty array
          return []
        }
      }
    },

    /**
     * Get visible columns for column virtualization
     */
    visibleColumns() {
      if (!this.shouldUseColumnVirtualization) {
        return this.processedColumns || []
      }

      const columns = this.processedColumns || []
      return columns.slice(this.visibleColumnStartIndex, this.visibleColumnEndIndex + 1)
    },

    /**
     * Calculate top spacer height for virtual scrolling - ENHANCED WITH ERROR HANDLING
     */
    topSpacerHeight() {
      try {
        // CRITICAL FIX: Ensure _virtualScrollState is initialized before accessing
        const virtualScrollState = this.ensureVirtualScrollState()

        // ENHANCED: Access multiple reactive dependencies
        const currentStartIndex = this.startIndex || 0
        const shouldUseVirtual = this.shouldUseVirtualScrolling
        // Access revision to ensure reactivity
        virtualScrollState.revision

        if (!shouldUseVirtual) return 0

        const rowHeight = this.rowHeight || 40
        const height = Math.max(0, currentStartIndex) * rowHeight

        return height
      } catch (error) {
        console.error('Vue2DataTable: Error calculating top spacer height:', error)
        return 0 // Safe fallback
      }
    },

    /**
     * Calculate bottom spacer height for virtual scrolling - ENHANCED WITH ERROR HANDLING
     */
    bottomSpacerHeight() {
      try {
        // CRITICAL FIX: Ensure _virtualScrollState is initialized before accessing
        const virtualScrollState = this.ensureVirtualScrollState()

        // ENHANCED: Access multiple reactive dependencies
        const currentEndIndex = this.endIndex || 0
        const currentFilteredItems = this.filteredItems
        const shouldUseVirtual = this.shouldUseVirtualScrolling
        // Access revision to ensure reactivity
        virtualScrollState.revision

        if (!shouldUseVirtual) return 0

        const totalItems = currentFilteredItems ? currentFilteredItems.length : 0
        const rowHeight = this.rowHeight || 40
        const remainingItems = Math.max(0, totalItems - (currentEndIndex + 1))

        return remainingItems * rowHeight
      } catch (error) {
        console.error('Vue2DataTable: Error calculating bottom spacer height:', error)
        return 0 // Safe fallback
      }
    },

    /**
     * Calculate total virtual height
     */
    totalVirtualHeight() {
      if (!this.shouldUseVirtualScrolling) return 'auto'
      
      const totalItems = this.filteredItems ? this.filteredItems.length : 0
      const rowHeight = this.rowHeight || 40
      return totalItems * rowHeight
    },

    /**
     * Calculate left spacer width for column virtualization
     */
    leftSpacerWidth() {
      if (!this.shouldUseColumnVirtualization) return 0
      
      let width = 0
      const columns = this.processedColumns || []
      for (let i = 0; i < this.visibleColumnStartIndex && i < columns.length; i++) {
        width += this.getColumnWidth(columns[i])
      }
      return width
    },

    /**
     * Calculate right spacer width for column virtualization
     */
    rightSpacerWidth() {
      if (!this.shouldUseColumnVirtualization) return 0
      
      let width = 0
      const columns = this.processedColumns || []
      for (let i = this.visibleColumnEndIndex + 1; i < columns.length; i++) {
        width += this.getColumnWidth(columns[i])
      }
      return width
    }
  },

  mounted() {
    this.initializeVirtualScrolling()
  },

  beforeDestroy() {
    this.cleanupVirtualScrolling()
  },

  methods: {
    /**
     * Ensure _virtualScrollState is properly initialized - CRITICAL FIX WITH ERROR HANDLING
     */
    ensureVirtualScrollState() {
      try {
        if (!this._virtualScrollState) {
          this._virtualScrollState = {
            revision: 0,
            lastCalculation: null,
            isUpdating: false
          }

          if (process.env.NODE_ENV === 'development') {
            console.log('Vue2DataTable: _virtualScrollState was undefined, initializing...')
          }
        }

        // Validate the state object structure
        if (typeof this._virtualScrollState !== 'object' ||
            this._virtualScrollState.revision === undefined ||
            this._virtualScrollState.lastCalculation === undefined ||
            this._virtualScrollState.isUpdating === undefined) {

          console.warn('Vue2DataTable: _virtualScrollState structure is invalid, reinitializing...')
          this._virtualScrollState = {
            revision: 0,
            lastCalculation: null,
            isUpdating: false
          }
        }

        return this._virtualScrollState
      } catch (error) {
        console.error('Vue2DataTable: Error ensuring virtual scroll state:', error)

        // Emergency fallback - create a new state object
        const fallbackState = {
          revision: 0,
          lastCalculation: null,
          isUpdating: false
        }

        try {
          this._virtualScrollState = fallbackState
        } catch (assignError) {
          console.error('Vue2DataTable: Cannot assign virtual scroll state:', assignError)
        }

        return fallbackState
      }
    },

    /**
     * Initialize virtual scrolling - ENHANCED WITH STATE SAFETY
     */
    initializeVirtualScrolling() {
      // CRITICAL FIX: Ensure virtual scroll state is initialized first
      this.ensureVirtualScrollState()

      this.$nextTick(() => {
        // Create virtual list calculator
        this.virtualListCalculator = createVirtualListCalculator({
          itemHeight: this.rowHeight || 40,
          containerHeight: this.containerHeight || 600,
          bufferSize: this.bufferSize || 5,
          overscan: this.overscan || 3
        })

        this.setupResizeObserver()
        this.updateContainerDimensions()

        this.$nextTick(() => {
          this.calculateVisibleRange()
          this.calculateVisibleColumns()

          if (process.env.NODE_ENV === 'development' && this.enablePerformanceMonitoring) {
            console.log('Virtual scrolling initialized:', {
              containerHeight: this.containerHeight,
              containerWidth: this.containerWidth,
              rowHeight: this.rowHeight,
              itemCount: this.filteredItems ? this.filteredItems.length : 0,
              startIndex: this.startIndex,
              endIndex: this.endIndex,
              virtualScrollState: this._virtualScrollState
            })
          }
        })
      })
    },

    /**
     * Setup resize observer for container
     */
    setupResizeObserver() {
      if (typeof ResizeObserver !== 'undefined' && this.$refs.tableContainer) {
        this.resizeObserver = new ResizeObserver(
          throttlePerformance(this.handleContainerResize.bind(this), 100)
        )
        this.resizeObserver.observe(this.$refs.tableContainer)
      }
    },

    /**
     * Handle container resize
     */
    handleContainerResize(entries) {
      if (entries && entries.length > 0) {
        const entry = entries[0]
        this.containerHeight = entry.contentRect.height
        this.containerWidth = entry.contentRect.width
        this.calculateVisibleRange()
        this.calculateVisibleColumns()
      }
    },

    /**
     * Update container dimensions
     */
    updateContainerDimensions() {
      if (this.$refs.tableContainer) {
        const rect = this.$refs.tableContainer.getBoundingClientRect()
        const newHeight = rect.height || 600
        const newWidth = rect.width || 800

        if (Math.abs(this.containerHeight - newHeight) > 1 || Math.abs(this.containerWidth - newWidth) > 1) {
          this.containerHeight = newHeight
          this.containerWidth = newWidth

          if (process.env.NODE_ENV === 'development' && this.enablePerformanceMonitoring) {
            console.log('Container dimensions updated:', {
              height: this.containerHeight,
              width: this.containerWidth
            })
          }
        }
      } else {
        if (this.containerHeight === 0) this.containerHeight = 600
        if (this.containerWidth === 0) this.containerWidth = 800
      }
    },

    /**
     * Handle wheel event for controlled scroll sensitivity
     */
    handleWheel(event) {
      if (!this.shouldUseVirtualScrolling || !this.smoothScrollEnabled) return

      event.preventDefault()

      const deltaY = event.deltaY * this.wheelScrollMultiplier * this.scrollSensitivity
      const deltaX = event.deltaX * this.wheelScrollMultiplier * this.scrollSensitivity

      // Update target scroll positions
      this.targetScrollTop = Math.max(0, this.targetScrollTop + deltaY)
      this.targetScrollLeft = Math.max(0, this.targetScrollLeft + deltaX)

      // Set wheel scrolling flag
      this.isWheelScrolling = true

      // Clear existing wheel timeout
      if (this.wheelScrollTimeout) {
        clearTimeout(this.wheelScrollTimeout)
      }

      // Start smooth scroll animation
      this.startSmoothScroll()

      // Reset wheel scrolling flag after delay
      this.wheelScrollTimeout = setTimeout(() => {
        this.isWheelScrolling = false
      }, 150)
    },

    /**
     * Start smooth scroll animation
     */
    startSmoothScroll() {
      if (this.scrollAnimationFrame) {
        cancelAnimationFrame(this.scrollAnimationFrame)
      }

      const animate = () => {
        const container = this.$refs.tableContainer
        if (!container) return

        const currentScrollTop = container.scrollTop
        const currentScrollLeft = container.scrollLeft

        // Calculate smooth interpolation
        const scrollTopDiff = this.targetScrollTop - currentScrollTop
        const scrollLeftDiff = this.targetScrollLeft - currentScrollLeft

        // Use easing for smooth animation
        const easing = 0.15 // Adjust for smoother/faster animation
        const newScrollTop = currentScrollTop + (scrollTopDiff * easing)
        const newScrollLeft = currentScrollLeft + (scrollLeftDiff * easing)

        // Apply scroll if there's significant difference
        if (Math.abs(scrollTopDiff) > 1 || Math.abs(scrollLeftDiff) > 1) {
          container.scrollTop = newScrollTop
          container.scrollLeft = newScrollLeft

          // Continue animation
          this.scrollAnimationFrame = requestAnimationFrame(animate)
        } else {
          // Animation complete, set final position
          container.scrollTop = this.targetScrollTop
          container.scrollLeft = this.targetScrollLeft
          this.scrollAnimationFrame = null
        }
      }

      this.scrollAnimationFrame = requestAnimationFrame(animate)
    },

    /**
     * Handle scroll event - ENHANCED PERFORMANCE FIX WITH SENSITIVITY CONTROL
     */
    handleScroll(event) {
      if (!this.shouldUseVirtualScrolling) return

      const target = event.target
      const newScrollTop = target.scrollTop
      const newScrollLeft = target.scrollLeft

      // Update target positions when not wheel scrolling
      if (!this.isWheelScrolling) {
        this.targetScrollTop = newScrollTop
        this.targetScrollLeft = newScrollLeft
      }

      // ENHANCED: More precise change detection
      const scrollTopChanged = Math.abs(newScrollTop - this.scrollTop) >= 1
      const scrollLeftChanged = Math.abs(newScrollLeft - this.scrollLeft) >= 1

      if (!scrollTopChanged && !scrollLeftChanged) {
        return
      }

      // Update scroll state immediately
      const oldScrollTop = this.scrollTop
      this.scrollDirection = newScrollTop > oldScrollTop ? 'down' : 'up'
      this.scrollTop = newScrollTop
      this.scrollLeft = newScrollLeft
      this.lastScrollTime = performance.now()
      this.isScrolling = true

      // Clear existing timeout
      if (this.scrollTimeout) {
        clearTimeout(this.scrollTimeout)
      }

      // CRITICAL FIX: Immediate calculation with performance optimization
      if (scrollTopChanged) {
        this.calculateVisibleRange()
      }
      if (scrollLeftChanged) {
        this.calculateVisibleColumns()
      }

      // ENHANCED: Optimized throttled event emission
      if (!this._throttledScrollHandler) {
        this._throttledScrollHandler = throttlePerformance(this.emitScrollEvent.bind(this), 16)
      }
      this._throttledScrollHandler()

      // ENHANCED: Adaptive timeout based on scroll speed
      const scrollSpeed = Math.abs(newScrollTop - oldScrollTop)
      const timeoutDelay = scrollSpeed > 100 ? 100 : 150

      this.scrollTimeout = setTimeout(() => {
        this.isScrolling = false
        this.onScrollEnd()
      }, timeoutDelay)
    },

    /**
     * Emit scroll event (throttled) - ENHANCED WITH NULL SAFETY
     */
    emitScrollEvent() {
      // CRITICAL FIX: Ensure virtual scroll state is initialized
      const virtualScrollState = this.ensureVirtualScrollState()

      this.$emit('scroll', {
        scrollTop: this.scrollTop || 0,
        scrollLeft: this.scrollLeft || 0,
        direction: this.scrollDirection || 'down',
        startIndex: this.startIndex || 0,
        endIndex: this.endIndex || 0,
        visibleCount: this.visibleRowCount || 0,
        isScrolling: this.isScrolling || false,
        revision: virtualScrollState.revision
      })
    },

    /**
     * Handle scroll end
     */
    onScrollEnd() {
      this.$emit('scroll-end', {
        scrollTop: this.scrollTop,
        scrollLeft: this.scrollLeft
      })
    },

    /**
     * Calculate visible row range - ENHANCED PERFORMANCE & ACCURACY WITH NULL SAFETY
     */
    calculateVisibleRange() {
      // CRITICAL FIX: Ensure virtual scroll state is initialized
      const virtualScrollState = this.ensureVirtualScrollState()

      // ENHANCED: Prevent recursive calls and race conditions
      if (virtualScrollState.isUpdating) {
        return
      }

      virtualScrollState.isUpdating = true

      try {
        if (!this.shouldUseVirtualScrolling) {
          const itemCount = this.filteredItems ? this.filteredItems.length : 0
          this._updateIndices(0, Math.max(0, itemCount - 1), itemCount)
          return
        }

        const itemCount = this.filteredItems ? this.filteredItems.length : 0
        if (itemCount === 0) {
          this._updateIndices(0, 0, 0)
          return
        }

        this.updateContainerDimensions()

        const rowHeight = Math.max(1, this.rowHeight || 40)
        const containerHeight = Math.max(100, this.containerHeight || 600)
        const overscan = Math.max(0, this.overscan || 3)
        const bufferSize = Math.max(0, this.bufferSize || 5)

        // ENHANCED: More accurate visible range calculation
        const scrollTop = Math.max(0, this.scrollTop || 0)
        const visibleStartIndex = Math.floor(scrollTop / rowHeight)
        const visibleItemCount = Math.ceil(containerHeight / rowHeight) + 1
        const visibleEndIndex = Math.min(
          itemCount - 1,
          visibleStartIndex + visibleItemCount - 1
        )

        // ENHANCED: Apply overscan and buffer with bounds checking
        const bufferedStartIndex = Math.max(0, visibleStartIndex - overscan - bufferSize)
        const bufferedEndIndex = Math.min(itemCount - 1, visibleEndIndex + overscan + bufferSize)

        const visibleCount = Math.max(0, bufferedEndIndex - bufferedStartIndex + 1)

        // ENHANCED: Only update if indices actually changed
        if (this.startIndex !== bufferedStartIndex ||
            this.endIndex !== bufferedEndIndex ||
            this.visibleRowCount !== visibleCount) {

          this._updateIndices(bufferedStartIndex, bufferedEndIndex, visibleCount)

          if (process.env.NODE_ENV === 'development' && this.enablePerformanceMonitoring) {
            console.log('Virtual Scroll Range Updated:', {
              scrollTop,
              rowHeight,
              containerHeight,
              itemCount,
              visibleStartIndex,
              visibleEndIndex,
              bufferedStartIndex,
              bufferedEndIndex,
              visibleCount,
              revision: virtualScrollState.revision,
              timestamp: performance.now()
            })
          }
        }
      } catch (error) {
        console.error('Vue2DataTable: Error calculating visible range:', error)
        // Fallback to safe values
        const itemCount = this.filteredItems ? this.filteredItems.length : 0
        this._updateIndices(0, Math.max(0, Math.min(itemCount - 1, 50)), Math.min(itemCount, 51))
      } finally {
        virtualScrollState.isUpdating = false
      }
    },

    /**
     * Efficiently update virtual scroll indices - FIXED WITH NULL SAFETY
     */
    _updateIndices(newStartIndex, newEndIndex, newVisibleCount) {
      // CRITICAL FIX: Ensure virtual scroll state is initialized
      const virtualScrollState = this.ensureVirtualScrollState()

      const hasChanged =
        this.startIndex !== newStartIndex ||
        this.endIndex !== newEndIndex ||
        this.visibleRowCount !== newVisibleCount

      if (hasChanged) {
        this.startIndex = newStartIndex
        this.endIndex = newEndIndex
        this.visibleRowCount = newVisibleCount

        // Increment revision to trigger reactivity
        virtualScrollState.revision++
        virtualScrollState.lastCalculation = Date.now()

        // Emit update event
        this.$emit('virtual-scroll-update', {
          startIndex: this.startIndex,
          endIndex: this.endIndex,
          visibleCount: this.visibleRowCount,
          revision: virtualScrollState.revision
        })

        if (process.env.NODE_ENV === 'development' && this.enablePerformanceMonitoring) {
          console.log('Indices updated:', {
            startIndex: this.startIndex,
            endIndex: this.endIndex,
            visibleCount: this.visibleRowCount,
            revision: virtualScrollState.revision
          })
        }
      }
    },

    /**
     * Calculate visible column range - ENHANCED ACCURACY
     */
    calculateVisibleColumns() {
      if (!this.shouldUseColumnVirtualization) {
        this.visibleColumnStartIndex = 0
        this.visibleColumnEndIndex = this.processedColumns ? this.processedColumns.length - 1 : 0
        this.totalColumnsWidth = this.calculateTotalColumnsWidth()
        return
      }

      const columns = this.processedColumns || []
      if (columns.length === 0) {
        this.visibleColumnStartIndex = 0
        this.visibleColumnEndIndex = 0
        this.totalColumnsWidth = 0
        return
      }

      // ENHANCED: Calculate total width for all columns
      this.totalColumnsWidth = this.calculateTotalColumnsWidth()

      const scrollLeft = Math.max(0, this.scrollLeft)
      const containerWidth = Math.max(100, this.containerWidth || 800)
      const bufferWidth = 200 // Extra width for smooth scrolling

      let accumulatedWidth = 0
      let startIndex = 0
      let endIndex = columns.length - 1

      // ENHANCED: Find start index with better accuracy
      for (let i = 0; i < columns.length; i++) {
        const columnWidth = this.getColumnWidth(columns[i])
        if (accumulatedWidth + columnWidth > scrollLeft) {
          startIndex = Math.max(0, i - 1) // Include one column before for smooth scrolling
          break
        }
        accumulatedWidth += columnWidth
      }

      // ENHANCED: Find end index with buffer
      accumulatedWidth = 0
      for (let i = startIndex; i < columns.length; i++) {
        const columnWidth = this.getColumnWidth(columns[i])
        accumulatedWidth += columnWidth

        // Include columns until we exceed visible area plus buffer
        if (accumulatedWidth >= containerWidth + bufferWidth) {
          endIndex = Math.min(i + 1, columns.length - 1) // Include one extra column
          break
        }
        endIndex = i
      }

      // ENHANCED: Ensure we always show at least a few columns
      const minVisibleColumns = 3
      if (endIndex - startIndex + 1 < minVisibleColumns) {
        endIndex = Math.min(startIndex + minVisibleColumns - 1, columns.length - 1)
      }

      this.visibleColumnStartIndex = startIndex
      this.visibleColumnEndIndex = endIndex

      if (process.env.NODE_ENV === 'development' && this.enablePerformanceMonitoring) {
        console.log('Column Virtualization:', {
          scrollLeft,
          containerWidth,
          totalColumns: columns.length,
          visibleStartIndex: startIndex,
          visibleEndIndex: endIndex,
          visibleCount: endIndex - startIndex + 1,
          totalColumnsWidth: this.totalColumnsWidth
        })
      }
    },

    /**
     * Calculate total width of all columns
     */
    calculateTotalColumnsWidth() {
      const columns = this.processedColumns || []
      return columns.reduce((total, column) => {
        return total + this.getColumnWidth(column)
      }, 0)
    },

    /**
     * Get column width - ENHANCED CALCULATION
     */
    getColumnWidth(column) {
      if (!column) return this.columnWidth || 150

      // ENHANCED: Handle different width formats
      if (column.width) {
        if (typeof column.width === 'number') {
          return Math.max(50, column.width) // Minimum width of 50px
        }
        if (typeof column.width === 'string') {
          // Handle px, %, em, rem units
          const match = column.width.match(/(\d+(?:\.\d+)?)(px|%|em|rem)?/)
          if (match) {
            const value = parseFloat(match[1])
            const unit = match[2] || 'px'

            if (unit === 'px') {
              return Math.max(50, value)
            } else if (unit === '%') {
              // Convert percentage to pixels based on container width
              const containerWidth = this.containerWidth || 800
              return Math.max(50, (value / 100) * containerWidth)
            } else if (unit === 'em' || unit === 'rem') {
              // Assume 16px base font size
              return Math.max(50, value * 16)
            }
          }
        }
      }

      // ENHANCED: Special handling for specific column types
      if (column.key === 'select') {
        return 60 // Fixed width for selection column
      }

      // ENHANCED: Dynamic width based on column type
      if (column.type === 'number' || column.type === 'currency') {
        return 120
      } else if (column.type === 'date' || column.type === 'datetime') {
        return 140
      } else if (column.type === 'boolean') {
        return 80
      }

      return Math.max(50, this.columnWidth || 150)
    },

    /**
     * Scroll to specific row
     */
    scrollToRow(index) {
      if (!this.shouldUseVirtualScrolling) return

      const targetScrollTop = index * (this.rowHeight || 40)
      if (this.$refs.tableContainer) {
        this.$refs.tableContainer.scrollTop = targetScrollTop
      }
    },

    /**
     * Scroll to specific column
     */
    scrollToColumn(index) {
      if (!this.shouldUseColumnVirtualization) return

      let targetScrollLeft = 0
      const columns = this.processedColumns || []
      
      for (let i = 0; i < index && i < columns.length; i++) {
        targetScrollLeft += this.getColumnWidth(columns[i])
      }

      if (this.$refs.tableContainer) {
        this.$refs.tableContainer.scrollLeft = targetScrollLeft
      }
    },

    /**
     * Get row offset for virtual positioning
     */
    getRowOffset(index) {
      return (this.startIndex + index) * (this.rowHeight || 40)
    },

    /**
     * Check if row is visible
     */
    isRowVisible(index) {
      return index >= this.startIndex && index <= this.endIndex
    },

    /**
     * Check if column is visible
     */
    isColumnVisible(index) {
      return index >= this.visibleColumnStartIndex && index <= this.visibleColumnEndIndex
    },

    /**
     * Cleanup virtual scrolling - ENHANCED MEMORY MANAGEMENT
     */
    cleanupVirtualScrolling() {
      // ENHANCED: Comprehensive cleanup of all resources
      try {
        // Clean up ResizeObserver
        if (this.resizeObserver) {
          this.resizeObserver.disconnect()
          this.resizeObserver = null
        }

        // Clean up all timeouts
        if (this.scrollTimeout) {
          clearTimeout(this.scrollTimeout)
          this.scrollTimeout = null
        }

        if (this._scrollUpdateTimeout) {
          clearTimeout(this._scrollUpdateTimeout)
          this._scrollUpdateTimeout = null
        }

        if (this.wheelScrollTimeout) {
          clearTimeout(this.wheelScrollTimeout)
          this.wheelScrollTimeout = null
        }

        // Clean up animation frames
        if (this.scrollAnimationFrame) {
          cancelAnimationFrame(this.scrollAnimationFrame)
          this.scrollAnimationFrame = null
        }

        // Clean up throttled functions
        if (this._throttledScrollHandler) {
          this._throttledScrollHandler = null
        }

        // Clean up virtual list calculator
        if (this.virtualListCalculator) {
          this.virtualListCalculator = null
        }

        // Clean up event listeners if any were added
        if (this.$refs.tableContainer) {
          this.$refs.tableContainer.removeEventListener('scroll', this.handleScroll)
          this.$refs.tableContainer.removeEventListener('wheel', this.handleWheel)
        }

        // Reset all virtual scroll state
        this.resetVirtualScrollState()

        // Force garbage collection hint
        if (window.gc && process.env.NODE_ENV === 'development') {
          setTimeout(() => window.gc(), 100)
        }
      } catch (error) {
        console.error('Vue2DataTable: Error during virtual scroll cleanup:', error)
      }
    },

    /**
     * Force recalculation of virtual scrolling
     */
    recalculateVirtualScrolling() {
      this.$nextTick(() => {
        this.updateContainerDimensions()
        this.calculateVisibleRange()
        this.calculateVisibleColumns()
      })
    },

    /**
     * Force reactivity update for virtual scrolling - ENHANCED WITH NULL SAFETY
     */
    forceVirtualScrollUpdate() {
      // CRITICAL FIX: Ensure virtual scroll state is initialized
      const virtualScrollState = this.ensureVirtualScrollState()

      virtualScrollState.revision++
      this.$nextTick(() => {
        if (this.shouldUseVirtualScrolling) {
          this.calculateVisibleRange()
        }
      })
    },

    /**
     * Reset virtual scroll state - ENHANCED WITH SAFE INITIALIZATION
     */
    resetVirtualScrollState() {
      // CRITICAL FIX: Always ensure proper initialization
      this._virtualScrollState = {
        revision: 0,
        lastCalculation: null,
        isUpdating: false
      }

      this.startIndex = 0
      this.endIndex = 0
      this.visibleRowCount = 0
      this.scrollTop = 0
      this.scrollLeft = 0

      // Reset scroll sensitivity state
      this.targetScrollTop = 0
      this.targetScrollLeft = 0
      this.isWheelScrolling = false

      if (process.env.NODE_ENV === 'development') {
        console.log('Vue2DataTable: Virtual scroll state reset')
      }
    }
  },

  watch: {
    filteredItems: {
      handler(newItems, oldItems) {
        if (!oldItems || newItems.length !== oldItems.length || newItems !== oldItems) {
          this.$nextTick(() => {
            this.calculateVisibleRange()

            if (process.env.NODE_ENV === 'development' && this.enablePerformanceMonitoring) {
              console.log('filteredItems changed:', {
                oldLength: oldItems ? oldItems.length : 0,
                newLength: newItems ? newItems.length : 0,
                startIndex: this.startIndex,
                endIndex: this.endIndex,
                revision: this._virtualScrollState.revision
              })
            }
          })
        }
      },
      immediate: true
    },

    processedColumns: {
      handler() {
        this.$nextTick(() => {
          this.calculateVisibleColumns()
        })
      },
      immediate: true
    },

    rowHeight() {
      if (this.virtualListCalculator) {
        this.virtualListCalculator = createVirtualListCalculator({
          itemHeight: this.rowHeight || 40,
          containerHeight: this.containerHeight || 600,
          bufferSize: this.bufferSize || 5,
          overscan: this.overscan || 3
        })
      }
      this.forceVirtualScrollUpdate()
    },

    columnWidth() {
      this.calculateVisibleColumns()
    },

    shouldUseVirtualScrolling: {
      handler(newValue, oldValue) {
        if (newValue && newValue !== oldValue) {
          this.$nextTick(() => {
            this.initializeVirtualScrolling()
          })
        } else if (!newValue && oldValue) {
          const itemCount = this.filteredItems ? this.filteredItems.length : 0
          this._updateIndices(0, Math.max(0, itemCount - 1), itemCount)
        }
      },
      immediate: true
    },

    containerHeight(newHeight, oldHeight) {
      if (Math.abs(newHeight - oldHeight) > 1 && this.shouldUseVirtualScrolling) {
        this.$nextTick(() => {
          this.calculateVisibleRange()
        })
      }
    }

    // REMOVED the scrollTop watcher as it was causing issues with the scroll handler
  }
}